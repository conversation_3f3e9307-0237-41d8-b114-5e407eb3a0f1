# Google Docs Credential Configuration for n8n AI Agent

This guide provides step-by-step instructions for setting up Google Docs as a direct tool/sub-node within n8n AI Agent for real-time knowledge base access.

## Overview

Using Google Docs as a direct tool in n8n AI Agent allows your AI to:
- Read Google Docs content in real-time during conversations
- Access up-to-date information without preprocessing
- Use documents as a knowledge base for customer support, FAQs, policies, etc.
- Automatically incorporate document content into AI responses

## Why OAuth is Required (Unfortunately, No Way to Skip)

**You're right - this seems overly complex for a simple automation!** However, Google requires OAuth for ALL programmatic access to Google Docs, even for reading your own documents. Here's why:

### Google's Security Model
- **No API Keys for Docs**: Unlike some Google services, Google Docs API doesn't support simple API key authentication
- **User Context Required**: Google needs to know WHICH user's documents to access
- **Scope-based Permissions**: OAuth allows fine-grained control over what the integration can access

### What You're Actually Creating
You're not creating a "public app" - you're creating:
- **Personal Integration**: Only for your own use
- **Private OAuth Client**: Not published or reviewed by Google
- **Internal Tool**: Just a way for n8n to authenticate as "you" to access your docs

### The "App" is Just Authentication
Think of it as creating a "key" that allows n8n to say "I'm acting on behalf of [your name] to read their Google Docs"

## Alternative Approaches (If You Want to Avoid OAuth)

### Option 1: Public Google Docs (Read-Only)
If your documents can be public, you can:
1. Set Google Doc sharing to "Anyone with the link can view"
2. Use n8n's **HTTP Request** node to fetch the document as plain text
3. URL format: `https://docs.google.com/document/d/[DOCUMENT_ID]/export?format=txt`

**Pros**: No OAuth needed, very simple
**Cons**: Document must be public, limited formatting, no real-time updates

### Option 2: Copy Content to Other Platforms
- **Notion**: Has simpler API authentication
- **Airtable**: Uses API keys instead of OAuth
- **Simple text files**: Store in GitHub, Dropbox, etc.
- **n8n Form**: Create a simple form to update content

### Option 3: Manual Content Updates
- Copy/paste content into n8n workflow variables
- Use n8n's built-in data storage
- Update content through n8n interface when needed

## If You Must Use Google Docs (OAuth Required)

Unfortunately, if you want to use Google Docs with proper authentication and private documents, OAuth is the ONLY way Google allows this. Here's why it's actually simpler than it looks:

### Simplified Understanding
1. **You're not building an app for others** - just for yourself
2. **One-time setup** - Configure once, use forever
3. **Google's requirement** - Not n8n's choice
4. **5-10 minutes total** - Faster than migrating content elsewhere

## Prerequisites

- Google Cloud Console account (free)
- n8n instance (Cloud or self-hosted)
- Google Docs document(s) to use as knowledge base

## Step 1: Create Google Cloud Console Project

1. **Log in to Google Cloud Console**
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Sign in with your Google account

2. **Create New Project**
   - Click the project dropdown in the top navigation
   - Select **"New Project"** or go to [New Project page](https://console.cloud.google.com/projectcreate)
   - Enter a **Project name** (e.g., "n8n-google-docs-integration")
   - Select the **Location** for your project
   - Click **"Create"**

3. **Verify Project Selection**
   - Check the project dropdown shows your new project
   - If not, select your newly created project

## Step 2: Enable Required APIs

1. **Navigate to APIs & Services**
   - Go to [Google Cloud Console - Library](https://console.cloud.google.com/apis/library)
   - Ensure you're in the correct project

2. **Enable Google Docs API**
   - Search for "Google Docs API"
   - Click on the result
   - Click **"ENABLE"**

3. **Enable Google Drive API** (Required)
   - Search for "Google Drive API"
   - Click on the result
   - Click **"ENABLE"**
   
   > **Note**: Google Drive API is required for Google Docs integration in n8n

## Step 3: Configure OAuth Consent Screen (Simplified for Personal Use)

> **Remember**: You're not creating a public app! This is just Google's way of letting n8n access YOUR documents.

1. **Access OAuth Consent Screen**
   - Go to **APIs & Services > OAuth consent screen**
   - Click **"Get started"** if first time

2. **Choose User Type** (Choose External - It's Simpler)
   - Select **"External"** (even for personal use)
   - This allows you to use it without Google Workspace
   - Click **"Create"**

3. **Configure App Information** (Minimal Required Fields)
   - **App name**: "My n8n Integration" (anything you want)
   - **User support email**: Your email address
   - **Developer contact information**: Your email address
   - **Leave everything else blank** - not needed for personal use

4. **Add Authorized Domains**
   - In **"Authorized domains"** section, click **"Add domain"**
   - For n8n Cloud: Add `n8n.cloud`
   - For self-hosted: Add your n8n instance domain (e.g., `yourdomain.com`)
   - Click **"Save and Continue"**

5. **Skip Scopes and Test Users**
   - Click **"Save and Continue"** through the remaining screens
   - You don't need to add scopes manually - n8n handles this
   - No test users needed for personal use

## Step 4: Create OAuth Client Credentials

1. **Navigate to Credentials**
   - Go to **APIs & Services > Credentials**
   - Click **"+ Create Credentials"**
   - Select **"OAuth client ID"**

2. **Configure OAuth Client**
   - **Application type**: Select **"Web application"**
   - **Name**: Enter descriptive name (e.g., "n8n Google Docs Tool")

3. **Set Authorized Redirect URIs**
   - Copy the **OAuth Redirect URL** from your n8n credential setup:
     ```
     https://oauth.n8n.cloud/oauth2/callback
     ```
   - For self-hosted n8n, use:
     ```
     https://your-n8n-domain.com/rest/oauth2-credential/callback
     ```
   - Paste this URL in **"Authorized redirect URIs"**
   - Click **"Create"**

4. **Save Credentials**
   - Copy the **Client ID** (long string starting with numbers)
   - Copy the **Client Secret** (shorter string with letters/numbers)
   - Keep these secure - you'll need them for n8n

## Step 5: Configure n8n Credentials

1. **Create New Credential in n8n**
   - In n8n, go to **Settings > Credentials**
   - Click **"Add Credential"**
   - Search for and select **"Google OAuth2 API"** or **"Google"**

2. **Fill Credential Fields**
   ```
   OAuth Redirect URL: https://oauth.n8n.cloud/oauth2/callback
   Client ID: [Paste your Google Client ID]
   Client Secret: [Paste your Google Client Secret]
   ```

3. **Authenticate with Google**
   - Click **"Sign in with Google"**
   - Choose your Google account
   - Grant permissions when prompted
   - You should see "Authentication successful"

4. **Save Credential**
   - Give your credential a descriptive name (e.g., "Google Docs Knowledge Base")
   - Click **"Save"**

## Step 6: Set Up AI Agent with Google Docs Tool

1. **Create AI Agent Workflow**
   ```
   Chat Trigger → AI Agent → Google Docs Tool (sub-node)
                      ↓
                 Chat Model (OpenAI/Gemini)
   ```

2. **Configure Google Docs Tool**
   - Connect **Google Docs** node as sub-node to AI Agent
   - **Tool Name**: "Knowledge Base Reader"
   - **Tool Description**: "Use this tool to access company policies, FAQs, and documentation stored in Google Docs"
   - **Credentials**: Select your Google credential
   - **Operation**: "Get" (to read document content)

3. **Configure Document Access**
   - **Document ID**: Extract from Google Docs URL
     ```
     https://docs.google.com/document/d/[DOCUMENT_ID]/edit
     ```
   - Copy the long string between `/d/` and `/edit`

4. **Set AI Agent System Message**
   ```
   You are a helpful assistant with access to our knowledge base stored in Google Docs.

   When users ask questions:
   1. Use the Google Docs tool to retrieve relevant information
   2. Base your answers on the document content
   3. If information isn't in the docs, clearly state that
   4. Always provide accurate, up-to-date information

   Use the Knowledge Base Reader tool before answering any questions.
   ```

## Step 7: Test the Integration

1. **Test Document Access**
   - Execute the Google Docs node manually
   - Verify it returns document content
   - Check for any authentication errors

2. **Test AI Agent**
   - Use the Chat interface
   - Ask questions related to your document content
   - Verify the AI uses the Google Docs tool
   - Confirm responses include document information

## Troubleshooting

### Common Issues

**"Google hasn't verified this app" Warning**
- This is normal for new OAuth apps
- Click "Advanced" → "Go to [app name] (unsafe)" to proceed
- For production, consider Google app verification

**Authentication Errors**
- Verify OAuth redirect URL matches exactly
- Check that both Google Docs and Drive APIs are enabled
- Ensure credentials are saved correctly in n8n

**Document Access Issues**
- Verify document ID is correct
- Ensure the Google account has access to the document
- Check document sharing permissions

**Tool Not Working in AI Agent**
- Verify Google Docs tool is connected as sub-node
- Check tool name and description are clear
- Ensure system message instructs AI to use the tool

### Best Practices

1. **Document Organization**
   - Use clear headings and structure
   - Keep documents under 10,000 words for best performance
   - Include table of contents for longer documents

2. **Security**
   - Use least-privilege access for Google accounts
   - Regularly review OAuth app permissions
   - Monitor API usage in Google Cloud Console

3. **Performance**
   - Consider document size limits
   - Monitor API rate limits
   - Cache frequently accessed content if needed

## Next Steps

- Set up multiple Google Docs tools for different knowledge areas
- Implement fallback responses for missing information
- Monitor usage and optimize document structure
- Consider hybrid approaches with other data sources

## Support Resources

- [n8n Google Docs Documentation](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googledocs/)
- [Google Cloud OAuth Documentation](https://developers.google.com/identity/protocols/oauth2)
- [n8n AI Agent Documentation](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent/)
